package urlutil

import (
	"reflect"
	"testing"
)

func TestFormatURL(t *testing.T) {
	tests := []struct {
		name string
		site string
		want []string
	}{
		{
			name: "basic url with host only",
			site: "https://example.com",
			want: []string{"example.com", "", "main", "default_home", "", "true"},
		},
		{
			name: "url with query params",
			site: "https://example.com?from=homepage",
			want: []string{"example.com", "homepage", "main", "default_home", "", "true"},
		},
		{
			name: "url with single segment",
			site: "https://example.com/games",
			want: []string{"example.com", "", "games", "default_home", "", "true"},
		},
		{
			name: "url with multiple segments",
			site: "https://example.com/games/123/play",
			want: []string{"example.com", "", "games", "play", "123", "false"},
		},
		{
			name: "url with main channel and segments",
			site: "https://example.com/main/456/match",
			want: []string{"example.com", "", "main", "match", "456", "false"},
		},
		{
			name: "url with trailing slash",
			site: "https://example.com/games/",
			want: []string{"example.com", "", "games", "default_home", "", "true"},
		},
		{
			name: "url with empty segments",
			site: "https://example.com///",
			want: []string{"example.com", "", "main", "default_home", "", "true"},
		},
		{
			name: "url with port number",
			site: "https://example.com:8080/games/789/play",
			want: []string{"example.com:8080", "", "games", "play", "789", "false"},
		},
		{
			name: "game url with multiple path segments",
			site: "http://zte.minigame.com/en/game/captain-pirate/play/m",
			want: []string{"zte.minigame.com", "", "game", "play", "captain-pirate", "false"},
		},
		{
			name: "game url with complex path structure",
			site: "https://play.minigame.com/en/pc/game/yeloli-dollhouse/play/recommended",
			want: []string{"play.minigame.com", "", "game", "play", "yeloli-dollhouse", "false"},
		},
		{
			name: "url with single language segment",
			site: "https://play.minigame.com/en/",
			want: []string{"play.minigame.com", "", "en", "default_home", "", "true"},
		},
		{
			name: "game url with query parameter appId",
			site: "https://televs.com/twa/game?appId=zombies-are-coming-tg",
			want: []string{"televs.com", "", "game", "play", "zombies-are-coming-tg", "false"},
		},
		{
			name: "url with custom channel segment",
			site: "https://televs.com/twa/",
			want: []string{"televs.com", "", "twa", "default_home", "", "true"},
		},
		{
			name: "url with custom channel segment",
			site: "https://play.minigame.com/en/pc/random",
			want: []string{"play.minigame.com", "", "en", "default_home", "", "true"},
		},
		{
			name: "game url with mini info",
			site: "https://blackview0609.minigame.com/en/game/mini/info",
			want: []string{"blackview0609.minigame.com", "", "game", "play", "", "false"},
		},
		{
			name: "pc action category page",
			site: "https://play.minigame.com/en/pc/action",
			want: []string{"play.minigame.com", "", "en", "default_home", "", "true"},
		},
		{
			name: "pc adventure category page",
			site: "https://play.minigame.com/en/pc/adventure",
			want: []string{"play.minigame.com", "", "en", "default_home", "", "true"},
		},
		{
			name: "pc cardboard category page",
			site: "https://play.minigame.com/en/pc/cardboard",
			want: []string{"play.minigame.com", "", "en", "default_home", "", "true"},
		},
		{
			name: "pc casual category page",
			site: "https://play.minigame.com/en/pc/casual",
			want: []string{"play.minigame.com", "", "en", "default_home", "", "true"},
		},
		{
			name: "pc hotgames category page",
			site: "https://play.minigame.com/en/pc/hotgames",
			want: []string{"play.minigame.com", "", "en", "default_home", "", "true"},
		},
		{
			name: "pc hotgames with query params",
			site: "https://play.minigame.com/en/pc/hotgames?gad_source=5&gad_campaignid=22672676384",
			want: []string{"play.minigame.com", "", "en", "default_home", "", "true"},
		},
		{
			name: "pc newgames category page",
			site: "https://play.minigame.com/en/pc/newgames",
			want: []string{"play.minigame.com", "", "en", "default_home", "", "true"},
		},
		{
			name: "pc racing category page",
			site: "https://play.minigame.com/en/pc/racing",
			want: []string{"play.minigame.com", "", "en", "default_home", "", "true"},
		},
		{
			name: "pc recently played category page",
			site: "https://play.minigame.com/en/pc/recetlyplayed",
			want: []string{"play.minigame.com", "", "en", "default_home", "", "true"},
		},
		{
			name: "pc simulation category page",
			site: "https://play.minigame.com/en/pc/simulation",
			want: []string{"play.minigame.com", "", "en", "default_home", "", "true"},
		},
		{
			name: "pc sports category page",
			site: "https://play.minigame.com/en/pc/sports",
			want: []string{"play.minigame.com", "", "en", "default_home", "", "true"},
		},
		{
			name: "pc strategy category page",
			site: "https://play.minigame.com/en/pc/strategy",
			want: []string{"play.minigame.com", "", "en", "default_home", "", "true"},
		},
		{
			name: "game links play page",
			site: "https://tcl.minigame.com/game/links/play",
			want: []string{"tcl.minigame.com", "", "game", "play", "", "false"},
		},
		{
			name: "game mini play page",
			site: "https://videomate.minigame.vip/game/mini/play",
			want: []string{"videomate.minigame.vip", "", "game", "play", "", "false"},
		},
		{
			name: "pc game with specific game name",
			site: "https://play.minigame.com/en/pc/game/crazy-excavator/play/recommended",
			want: []string{"play.minigame.com", "", "game", "play", "crazy-excavator", "false"},
		},
		{
			name: "mobile game with specific game name",
			site: "https://bullet0509.minigame.com/en/game/ant-world/play/m",
			want: []string{"bullet0509.minigame.com", "", "game", "play", "ant-world", "false"},
		},
		{
			name: "game with hyphenated name",
			site: "https://bullet060901.minigame.com/en/game/baby-dress-up/play/m",
			want: []string{"bullet060901.minigame.com", "", "game", "play", "baby-dress-up", "false"},
		},
		{
			name: "game with simple name",
			site: "https://videomatebox.minigame.vip/en/game/chop-io/play/m",
			want: []string{"videomatebox.minigame.vip", "", "game", "play", "chop-io", "false"},
		},
		{
			name: "game with mr prefix",
			site: "https://kingboat0605.minigame.com/en/game/mr-survivor/play/m",
			want: []string{"kingboat0605.minigame.com", "", "game", "play", "mr-survivor", "false"},
		},
		{
			name: "mobile game with m path segment",
			site: "https://play.minigame.com/en/m/game/chop-io/play/recommended2",
			want: []string{"play.minigame.com", "", "en", "play", "chop-io", "false"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := FormatURL(tt.site)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FormatURL() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_isMainChannel_Additional(t *testing.T) {
	type args struct {
		segment string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "empty string",
			args: args{
				segment: "",
			},
			want: true,
		},
		{
			name: "forward slash",
			args: args{
				segment: "/",
			},
			want: true,
		},
		{
			name: "home segment",
			args: args{
				segment: "home",
			},
			want: true,
		},
		{
			name: "privacy segment",
			args: args{
				segment: "privacy",
			},
			want: true,
		},
		{
			name: "terms segment",
			args: args{
				segment: "terms",
			},
			want: true,
		},
		{
			name: "gamebox segment",
			args: args{
				segment: "gamebox",
			},
			want: true,
		},
		{
			name: "today segment",
			args: args{
				segment: "today",
			},
			want: true,
		},
		{
			name: "game segment",
			args: args{
				segment: "game",
			},
			want: true,
		},
		{
			name: "v1 segment",
			args: args{
				segment: "v1",
			},
			want: true,
		},
		{
			name: "debug segment",
			args: args{
				segment: "debug",
			},
			want: true,
		},
		{
			name: "activity segment",
			args: args{
				segment: "activity",
			},
			want: true,
		},
		{
			name: "case sensitive check",
			args: args{
				segment: "MAIN",
			},
			want: false,
		},
		{
			name: "special characters",
			args: args{
				segment: "main!",
			},
			want: false,
		},
		{
			name: "whitespace segment",
			args: args{
				segment: " ",
			},
			want: false,
		},
		{
			name: "partial match",
			args: args{
				segment: "mainpage",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isMainChannel(tt.args.segment); got != tt.want {
				t.Errorf("isMainChannel() = %v, want %v", got, tt.want)
			}
		})
	}
}
