package urlutil

import (
	"net/url"
	"strconv"
	"strings"
)

// isMainChannel 判断是否是主渠道
func isMainChannel(segment string) bool {
	a := []string{"", "/", "home", "privacy", "terms", "gamebox", "today", "game", "v1", "main", "debug", "activity"}
	for _, b := range a {
		if b == segment {
			return true
		}
	}
	return false
}

func FormatURL(site string) []string {
	res := make([]string, 0)
	u, err := url.Parse(site)
	if err != nil {
		panic(err)
	}

	segments := strings.Split(strings.Trim(u.Path, "/"), "/")
	isRoot := false
	if len(segments) == 1 {
		isRoot = true
	}
	queryParams := u.Query()

	// Host
	res = append(res, u.Host)

	// From parameter
	res = append(res, queryParams.Get("from"))

	// 检查是否是游戏相关URL
	isGameURL := false
	gameIndex := -1
	for i, segment := range segments {
		if segment == "game" {
			isGameURL = true
			gameIndex = i
			break
		}
	}

	var subChannel, pageType, appID string

	if isGameURL {
		// 游戏相关URL的特殊处理
		subChannel = "game"
		pageType = "play"

		// 特殊处理某些游戏URL模式，这些应该被当作特殊页面，appID为空
		if len(segments) >= 3 && (segments[gameIndex+1] == "mini" || segments[gameIndex+1] == "links") {
			appID = ""
		} else {
			// 提取appID
			if appIdParam := queryParams.Get("appId"); appIdParam != "" {
				// 从query参数获取
				appID = appIdParam
			} else if gameIndex+1 < len(segments) && segments[gameIndex+1] != "" {
				// 从URL路径获取游戏名称
				appID = segments[gameIndex+1]
			}
		}
	} else {
		// 原有逻辑
		subChannel = segments[0]
		if isMainChannel(segments[0]) {
			subChannel = "main"
		}

		// 特殊处理：如果路径是 /en/pc/[任何内容] 这种格式，应该当作主页面处理
		if len(segments) == 3 && segments[1] == "pc" {
			pageType = "default_home"
			appID = ""
			isRoot = true
		} else {
			// 原有的pageType逻辑
			pageType = "default_home"
			if len(segments[0]) == 0 {
				pageType = "default_home"
			} else if len(segments) == 1 && subChannel != "main" {
				pageType = "default_home"
			} else if len(segments) == 1 && subChannel == "main" {
				pageType = segments[0]
			} else if len(segments) > 1 {
				pageType = segments[len(segments)-1]
			} else {
				pageType = segments[0]
			}

			// 原有的appID逻辑
			if len(segments) >= 3 {
				appID = segments[len(segments)-2]
			}
		}
	}

	res = append(res, subChannel)
	res = append(res, pageType)
	res = append(res, appID)
	res = append(res, strconv.FormatBool(isRoot))
	return res
}
