-- 修复 page_url_info 表中的URL解析问题
-- 基于修复后的 FormatURL 函数逻辑

BEGIN;

-- 1. 修复 /en/pc/[category] 格式的URL
-- 这些URL的 app_id 应该为空，page_type 应该为 'default_home'
UPDATE page_url_infos 
SET 
    app_id = '',
    page_type = 'default_home'
WHERE 
    page_url ~ 'https?://[^/]+/en/pc/[^/]+/?(\?.*)?$'
    AND sub_channel = 'en'
    AND (app_id != '' OR page_type != 'default_home');

-- 2. 修复 /game/mini/[action] 格式的URL  
-- 这些URL的 app_id 应该为空
UPDATE page_url_infos 
SET 
    app_id = ''
WHERE 
    page_url ~ 'https?://[^/]+/.*/game/mini/.*'
    AND sub_channel = 'game'
    AND page_type = 'play'
    AND app_id != '';

-- 3. 修复 /game/links/[action] 格式的URL
-- 这些URL的 app_id 应该为空  
UPDATE page_url_infos 
SET 
    app_id = ''
WHERE 
    page_url ~ 'https?://[^/]+/.*/game/links/.*'
    AND sub_channel = 'game'
    AND page_type = 'play'
    AND app_id != '';

-- 4. 修复 page_url_info_hkd 表 (香港数据)
-- 修复 /en/pc/[category] 格式的URL
UPDATE page_url_info_hkd 
SET 
    app_id = '',
    page_type = 'default_home'
WHERE 
    page_url ~ 'https?://[^/]+/en/pc/[^/]+/?(\?.*)?$'
    AND sub_channel = 'en'
    AND (app_id != '' OR page_type != 'default_home');

-- 修复 /game/mini/[action] 格式的URL
UPDATE page_url_info_hkd 
SET 
    app_id = ''
WHERE 
    page_url ~ 'https?://[^/]+/.*/game/mini/.*'
    AND sub_channel = 'game'
    AND page_type = 'play'
    AND app_id != '';

-- 修复 /game/links/[action] 格式的URL
UPDATE page_url_info_hkd 
SET 
    app_id = ''
WHERE 
    page_url ~ 'https?://[^/]+/.*/game/links/.*'
    AND sub_channel = 'game'
    AND page_type = 'play'
    AND app_id != '';

-- 5. 修复 page_url_ad_format_infos 表
-- 修复 /en/pc/[category] 格式的URL
UPDATE page_url_ad_format_infos 
SET 
    app_id = '',
    page_type = 'default_home'
WHERE 
    page_url ~ 'https?://[^/]+/en/pc/[^/]+/?(\?.*)?$'
    AND sub_channel = 'en'
    AND (app_id != '' OR page_type != 'default_home');

-- 修复 /game/mini/[action] 格式的URL
UPDATE page_url_ad_format_infos 
SET 
    app_id = ''
WHERE 
    page_url ~ 'https?://[^/]+/.*/game/mini/.*'
    AND sub_channel = 'game'
    AND page_type = 'play'
    AND app_id != '';

-- 修复 /game/links/[action] 格式的URL
UPDATE page_url_ad_format_infos 
SET 
    app_id = ''
WHERE 
    page_url ~ 'https?://[^/]+/.*/game/links/.*'
    AND sub_channel = 'game'
    AND page_type = 'play'
    AND app_id != '';

-- 6. 修复 page_url_ad_format_info_hkd 表
-- 修复 /en/pc/[category] 格式的URL
UPDATE page_url_ad_format_info_hkd 
SET 
    app_id = '',
    page_type = 'default_home'
WHERE 
    page_url ~ 'https?://[^/]+/en/pc/[^/]+/?(\?.*)?$'
    AND sub_channel = 'en'
    AND (app_id != '' OR page_type != 'default_home');

-- 修复 /game/mini/[action] 格式的URL
UPDATE page_url_ad_format_info_hkd 
SET 
    app_id = ''
WHERE 
    page_url ~ 'https?://[^/]+/.*/game/mini/.*'
    AND sub_channel = 'game'
    AND page_type = 'play'
    AND app_id != '';

-- 修复 /game/links/[action] 格式的URL
UPDATE page_url_ad_format_info_hkd 
SET 
    app_id = ''
WHERE 
    page_url ~ 'https?://[^/]+/.*/game/links/.*'
    AND sub_channel = 'game'
    AND page_type = 'play'
    AND app_id != '';

COMMIT;

-- 查询修复结果统计
SELECT 
    'page_url_infos' as table_name,
    COUNT(*) as total_rows,
    COUNT(CASE WHEN app_id = '' THEN 1 END) as empty_app_id_count,
    COUNT(CASE WHEN page_type = 'default_home' THEN 1 END) as default_home_count
FROM page_url_infos
UNION ALL
SELECT 
    'page_url_info_hkd' as table_name,
    COUNT(*) as total_rows,
    COUNT(CASE WHEN app_id = '' THEN 1 END) as empty_app_id_count,
    COUNT(CASE WHEN page_type = 'default_home' THEN 1 END) as default_home_count
FROM page_url_info_hkd
UNION ALL
SELECT 
    'page_url_ad_format_infos' as table_name,
    COUNT(*) as total_rows,
    COUNT(CASE WHEN app_id = '' THEN 1 END) as empty_app_id_count,
    COUNT(CASE WHEN page_type = 'default_home' THEN 1 END) as default_home_count
FROM page_url_ad_format_infos
UNION ALL
SELECT 
    'page_url_ad_format_info_hkd' as table_name,
    COUNT(*) as total_rows,
    COUNT(CASE WHEN app_id = '' THEN 1 END) as empty_app_id_count,
    COUNT(CASE WHEN page_type = 'default_home' THEN 1 END) as default_home_count
FROM page_url_ad_format_info_hkd;
