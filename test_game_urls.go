package main

import (
	"fmt"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/urlutil"
)

func main() {
	urls := []string{
		"https://play.minigame.com/en/pc/game/crazy-excavator/play/recommended",
		"https://bullet0509.minigame.com/en/game/ant-world/play/m",
		"https://bullet0509.minigame.com/en/game/crazy-ball/play/m",
		"https://bullet060901.minigame.com/en/game/baby-dress-up/play/m",
		"https://videomatebox.minigame.vip/en/game/chop-io/play/m",
		"https://play.minigame.com/en/pc/game/race-master/play/recommended",
		"https://bullet0509.minigame.com/en/game/two-player-games/play/m",
		"https://play.minigame.com/en/pc/game/royal-dream-wedding/play/recommended",
		"https://play.minigame.com/en/pc/game/heels-queen/play/recommended",
		"https://bullet060901.minigame.com/en/game/burger-bar/play/m",
	}

	for i, url := range urls {
		result := urlutil.FormatURL(url)
		fmt.Printf("%d. URL: %s\n", i+1, url)
		fmt.Printf("   Result: %v\n", result)
		fmt.Printf("   AppID (index 4): '%s'\n", result[4])
		fmt.Println("---")
	}
}
